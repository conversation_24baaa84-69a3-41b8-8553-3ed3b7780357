import json
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter.scrolledtext import ScrolledText
import webbrowser

class BookmarkNode:
    def __init__(self, name, node_type, url="", children=None, node_id=""):
        self.name = name
        self.type = node_type
        self.url = url
        self.children = children if children is not None else []
        self.id = node_id
        self.parent = None
        if children:
            for child in children:
                child.parent = self

class BookmarkManager:
    def __init__(self, root):
        self.root = root
        self.root.title("Chrome书签管理器")
        self.root.geometry("900x650")
        self.root.minsize(800, 500)
        
        # 设置主题
        self.style = ttk.Style()
        self.style.theme_use("clam")
        self.style.configure("Treeview", background="#f0f0f0", fieldbackground="#f0f0f0", font=('Segoe UI', 9))
        self.style.configure("Treeview.Heading", font=('Segoe UI', 9, 'bold'))
        self.style.map("Treeview", background=[('selected', '#4a6984')], foreground=[('selected', 'white')])
        
        # 创建主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分栏
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 左侧：书签树
        left_frame = ttk.Frame(paned_window, width=250)
        paned_window.add(left_frame, weight=1)
        
        tree_label = ttk.Label(left_frame, text="书签目录结构", font=('Segoe UI', 10, 'bold'))
        tree_label.pack(pady=(0, 5), anchor='w')
        
        self.tree_frame = ttk.Frame(left_frame)
        self.tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.tree = ttk.Treeview(self.tree_frame, columns=("type"), show="tree", selectmode="browse")
        self.tree.column("#0", width=200, minwidth=150)
        self.tree.column("type", width=60, minwidth=50)
        
        vsb = ttk.Scrollbar(self.tree_frame, orient="vertical", command=self.tree.yview)
        hsb = ttk.Scrollbar(self.tree_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        self.tree.grid(row=0, column=0, sticky="nsew")
        vsb.grid(row=0, column=1, sticky="ns")
        hsb.grid(row=1, column=0, sticky="ew")
        
        self.tree_frame.grid_rowconfigure(0, weight=1)
        self.tree_frame.grid_columnconfigure(0, weight=1)
        
        # 右侧：书签详情和搜索
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=2)
        
        # 搜索框
        search_frame = ttk.LabelFrame(right_frame, text="书签搜索")
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=40)
        search_entry.pack(side=tk.LEFT, padx=(5, 5), pady=5, fill=tk.X, expand=True)
        
        search_btn_frame = ttk.Frame(search_frame)
        search_btn_frame.pack(side=tk.RIGHT, padx=(0, 5), pady=5)
        
        global_search_btn = ttk.Button(search_btn_frame, text="全局搜索", width=12, 
                                     command=lambda: self.search_bookmarks(True))
        global_search_btn.pack(side=tk.LEFT, padx=2)
        
        folder_search_btn = ttk.Button(search_btn_frame, text="当前目录搜索", width=12, 
                                      command=lambda: self.search_bookmarks(False))
        folder_search_btn.pack(side=tk.LEFT, padx=2)
        
        # 书签列表
        list_label = ttk.Label(right_frame, text="书签列表", font=('Segoe UI', 10, 'bold'))
        list_label.pack(pady=(0, 5), anchor='w')
        
        self.list_frame = ttk.Frame(right_frame)
        self.list_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ("name", "url")
        self.bookmark_list = ttk.Treeview(self.list_frame, columns=columns, show="headings", selectmode="browse")
        self.bookmark_list.heading("name", text="名称")
        self.bookmark_list.heading("url", text="URL")
        self.bookmark_list.column("name", width=200, minwidth=150)
        self.bookmark_list.column("url", width=400, minwidth=300)
        
        list_vsb = ttk.Scrollbar(self.list_frame, orient="vertical", command=self.bookmark_list.yview)
        list_hsb = ttk.Scrollbar(self.list_frame, orient="horizontal", command=self.bookmark_list.xview)
        self.bookmark_list.configure(yscrollcommand=list_vsb.set, xscrollcommand=list_hsb.set)
        
        self.bookmark_list.grid(row=0, column=0, sticky="nsew")
        list_vsb.grid(row=0, column=1, sticky="ns")
        list_hsb.grid(row=1, column=0, sticky="ew")
        
        self.list_frame.grid_rowconfigure(0, weight=1)
        self.list_frame.grid_columnconfigure(0, weight=1)
        
        # 底部状态栏
        status_frame = ttk.Frame(root)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, anchor=tk.W)
        status_label.pack(fill=tk.X)
        
        # 绑定事件
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)
        self.bookmark_list.bind("<Double-1>", self.open_url)
        search_entry.bind("<Return>", lambda e: self.search_bookmarks(True))
        
        # 初始化
        self.bookmark_root = None
        self.current_folder = None
        self.bookmarks_data = None
        self.load_bookmarks()
        
        # 添加菜单
        self.create_menu()
    
    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="重新加载书签", command=self.load_bookmarks)
        file_menu.add_command(label="选择书签文件...", command=self.select_bookmark_file)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def select_bookmark_file(self):
        file_path = filedialog.askopenfilename(
            title="选择Chrome书签文件",
            filetypes=[("Bookmark files", "Bookmarks*"), ("All files", "*.*")]
        )
        if file_path:
            self.load_bookmarks(file_path)
    
    def show_about(self):
        about_text = """Chrome书签管理器 v1.0
        
功能：
1. 读取Chrome书签文件并展示目录结构
2. 点击目录可查看该目录下的书签
3. 支持全局搜索和当前目录搜索
4. 双击书签可在浏览器中打开
        
开发者: Python实用工具
        """
        messagebox.showinfo("关于", about_text)
    
    def load_bookmarks(self, file_path=None):
        default_path = os.path.expanduser("~") + r"\AppData\Local\Google\Chrome\User Data\Default\Bookmarks"
        file_path = file_path or default_path
        
        if not os.path.exists(file_path):
            messagebox.showerror("错误", f"找不到书签文件: {file_path}")
            self.status_var.set(f"错误: 找不到书签文件")
            return
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                self.bookmarks_data = json.load(f)
            
            # 清空树
            for i in self.tree.get_children():
                self.tree.delete(i)
            
            # 构建书签树
            self.bookmark_root = self.parse_bookmarks(self.bookmarks_data)
            self.populate_tree()
            self.status_var.set(f"已加载书签文件: {os.path.basename(file_path)}")
            
            # 默认选择第一个节点
            if self.tree.get_children():
                first_node = self.tree.get_children()[0]
                self.tree.selection_set(first_node)
                self.tree.focus(first_node)
                self.on_tree_select(None)
        
        except Exception as e:
            messagebox.showerror("错误", f"加载书签文件失败: {str(e)}")
            self.status_var.set(f"错误: 加载书签文件失败")
    
    def parse_bookmarks(self, data):
        """递归解析书签数据"""
        def parse_node(node_data):
            node_type = node_data.get("type", "folder")
            name = node_data.get("name", "未命名")
            url = node_data.get("url", "")
            node_id = node_data.get("id", "")
            
            if node_type == "folder" and "children" in node_data:
                children = [parse_node(child) for child in node_data["children"]]
                return BookmarkNode(name, node_type, url, children, node_id)
            else:
                return BookmarkNode(name, node_type, url, [], node_id)
        
        # 从根节点开始解析
        roots = data.get("roots", {})
        root_node = BookmarkNode("书签根目录", "folder", children=[], node_id="root")
        
        for root_type in ["bookmark_bar", "other", "synced"]:
            if root_type in roots:
                root_node.children.append(parse_node(roots[root_type]))
        
        return root_node
    
    def populate_tree(self):
        """将书签节点填充到树形视图中"""
        def add_node_to_tree(parent, node):
            if node.type == "folder":
                node_id = node.id
                tree_id = self.tree.insert(parent, "end", iid=node_id, text=node.name, 
                                         values=("文件夹",), tags=("folder",))
                for child in node.children:
                    add_node_to_tree(tree_id, child)
            else:
                # 书签不添加到树中，只在列表中显示
                pass
        
        # 添加根节点
        root_id = self.tree.insert("", "end", iid="root", text=self.bookmark_root.name, 
                                  values=("根目录",), tags=("root",))
        
        # 添加子节点
        for child in self.bookmark_root.children:
            add_node_to_tree(root_id, child)
        
        # 展开根节点
        self.tree.item(root_id, open=True)
    
    def on_tree_select(self, event):
        """当树节点被选中时更新书签列表"""
        selected = self.tree.selection()
        if not selected:
            return
        
        item_id = selected[0]
        self.current_folder = self.find_node_by_id(item_id)
        
        # 清空书签列表
        for i in self.bookmark_list.get_children():
            self.bookmark_list.delete(i)
        
        if self.current_folder:
            # 添加当前文件夹下的书签
            for child in self.current_folder.children:
                if child.type == "url":
                    self.bookmark_list.insert("", "end", values=(child.name, child.url))
            
            self.status_var.set(f"显示目录: {self.current_folder.name} - 书签数: {len(self.bookmark_list.get_children())}")
    
    def find_node_by_id(self, node_id):
        """根据节点ID查找节点"""
        def find_node(node, target_id):
            if node.id == target_id:
                return node
            for child in node.children:
                found = find_node(child, target_id)
                if found:
                    return found
            return None
        
        return find_node(self.bookmark_root, node_id)
    
    def search_bookmarks(self, global_search):
        """搜索书签"""
        query = self.search_var.get().strip().lower()
        if not query:
            messagebox.showinfo("提示", "请输入搜索关键词")
            return
        
        # 清空书签列表
        for i in self.bookmark_list.get_children():
            self.bookmark_list.delete(i)
        
        # 执行搜索
        results = []
        
        if global_search:
            # 全局搜索
            self.search_node(self.bookmark_root, query, results)
            self.status_var.set(f"全局搜索: '{query}' - 找到 {len(results)} 个结果")
        else:
            # 当前目录搜索
            if self.current_folder:
                self.search_in_folder(self.current_folder, query, results)
                self.status_var.set(f"当前目录搜索: '{query}' - 找到 {len(results)} 个结果")
            else:
                messagebox.showinfo("提示", "请先选择一个目录")
        
        # 显示结果
        for node in results:
            self.bookmark_list.insert("", "end", values=(node.name, node.url))
    
    def search_node(self, node, query, results):
        """递归搜索节点"""
        if node.type == "url":
            if query in node.name.lower() or query in node.url.lower():
                results.append(node)
        else:
            for child in node.children:
                self.search_node(child, query, results)
    
    def search_in_folder(self, folder, query, results):
        """在指定文件夹内搜索"""
        for child in folder.children:
            if child.type == "url":
                if query in child.name.lower() or query in child.url.lower():
                    results.append(child)
    
    def open_url(self, event):
        """在浏览器中打开选中的URL"""
        selected = self.bookmark_list.selection()
        if not selected:
            return
        
        item = self.bookmark_list.item(selected[0])
        url = item["values"][1]
        
        if url.startswith(("http://", "https://")):
            webbrowser.open(url)
        else:
            messagebox.showinfo("提示", f"无法打开URL: {url}")

if __name__ == "__main__":
    root = tk.Tk()
    app = BookmarkManager(root)
    root.mainloop()